<script lang="ts" generics="T extends string | number | Record<string, any>">
	import { cn } from '..';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';
	import type {
		HTMLInputAttributes,
		HTMLInputTypeAttribute,
		HTMLTextareaAttributes
	} from 'svelte/elements';
	import Select from '$lib/inputs/Select.svelte';
	import type { Snippet } from 'svelte';

	interface IProps {
		name: string;
		id?: string;

		type: HTMLInputTypeAttribute | 'textarea' | 'select';
		value: T;
		class?: string;

		mode?: 'add' | 'view' | 'edit';

		noLabel: boolean;
		label?: string;
		placeholder?: string;

		select_options?: Snippet;
		matcher?: keyof T;
		list?: T[];

		validation?: boolean;
	}

	let {
		name,
		id = name,

		noLabel = false,
		label,
		placeholder = label,

		type,
		value = $bindable(),

		mode = $bindable('add'),

		class: className,

		select_options,
		matcher,
		list,

		validation = true,

		...restProps
	}: IProps & Record<string, any> = $props();

	// Type-safe props for different element types
	const inputProps = $derived(
		type !== 'textarea' && type !== 'select' ? (restProps as HTMLInputAttributes) : {}
	);
	const textareaProps = $derived(type === 'textarea' ? (restProps as HTMLTextareaAttributes) : {});
</script>

<label for={name} class=" {noLabel ? '' : 'floating-label'}">
	{#if noLabel}
		<span class="sr-only">{label}</span>
	{/if}

	{#if type === 'textarea'}
		<textarea
			{name}
			{id}
			{placeholder}
			readonly={mode === 'view'}
			class={cn('textarea w-full', className ?? '')}
			class:border-dashed={mode === 'view'}
			bind:value
			{...textareaProps}
		></textarea>
	{:else if type === 'select'}
		{#key value}
			<Select
				{name}
				{id}
				bind:value
				{matcher}
				list={list ?? []}
				class={className ?? ''}
				{...restProps}
			>
				{@render select_options?.()}
			</Select>
		{/key}
	{:else}
		<input
			{type}
			{name}
			{id}
			{placeholder}
			readonly={mode === 'view'}
			class={cn('input w-full', className ?? '')}
			class:border-dashed={mode === 'view'}
			bind:value
			{...inputProps}
		/>
	{/if}

	{#if validation}
		<ValidationError {name} />
	{/if}
</label>
