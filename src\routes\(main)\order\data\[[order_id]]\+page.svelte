<script lang="ts">
	import Icon from '@iconify/svelte';
	import {
		getServiceOrderState,
		setServiceOrderState
	} from './serviceOrder/ServiceOrderState.svelte.js';
	import { rupiah } from '$lib/inputs/CurrencyState.svelte.js';
	import ServiceOrderTable from './components/ServiceOrderTable.svelte';

	import logoCombined from '$lib/images/logo-combined.webp';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import { getInvoiceState, setInvoiceState } from './serviceOrder/InvoiceState.svelte.js';
	import { setValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte.js';
	import { jenis<PERSON><PERSON>nan } from '$lib/schema/literal.js';
	import Currency from '$lib/inputs/Currency.svelte';

	const { data } = $props();

	let self = data.invoice.order;

	setServiceOrderState();
	const serviceOrder = getServiceOrderState();

	let invoiceState = $state(getInvoiceState());
	setInvoiceState(data.invoice);
	$effect(() => {
		invoiceState = getInvoiceState();
	});

	setValidationErrorState();
	$inspect(invoiceState.order);
</script>

<main class="flex min-h-full flex-col gap-2">
	<section class="h-fit shrink-0 overflow-auto">
		<div class="flex h-fit shrink-0 gap-2 border-b-2 border-b-gray-300">
			{#if invoiceState}
				<aside class="flex w-5/12 shrink-0 grow flex-col gap-2 p-4 text-xs">
					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit shrink-0 font-semibold">Nomor Polisi</p>
						<p class="w-2">:</p>
						<div class="flex grow items-center gap-4">
							<FormField
								noLabel
								name="nomor_polisi"
								placeholder="Nomor Polisi"
								type="text"
								bind:value={invoiceState.order.kendaraan.nomor_polisi}
								class="input-sm"
							></FormField>

							<div class="text-primary flex items-center gap-2">
								<p class="w-3/12 min-w-fit font-semibold">CC</p>
								<p class="w-2">:</p>
								<div class="grow">
									<FormField
										noLabel
										name="cc_kendaraan"
										placeholder="CC"
										type="number"
										bind:value={invoiceState.order.kendaraan.cc_kendaraan}
										step="50"
										class="input-sm"
									></FormField>
								</div>
							</div>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit font-semibold">Kendaraan</p>
						<p class="w-2">:</p>
						<div class="grow">
							<FormField
								noLabel
								name="nama_kendaraan"
								placeholder="Nama Kendaraan"
								type="text"
								class="input-sm"
								bind:value={invoiceState.order.kendaraan.nama_kendaraan}
							></FormField>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit font-semibold">Pemilik</p>
						<p class="w-2">:</p>
						<div class="grow">
							<FormField
								noLabel
								name="pemilik"
								placeholder="Pemilik"
								type="text"
								class="input-sm"
								bind:value={invoiceState.order.kendaraan.pemilik.nama}
							></FormField>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit font-semibold">Pengantar</p>
						<p class="w-2">:</p>
						<div class="grow">
							<FormField
								noLabel
								name="pengantar"
								placeholder="Pengantar"
								type="text"
								class="input-sm"
								bind:value={invoiceState.order.pengantar}
							></FormField>
						</div>
					</div>
				</aside>
			{/if}

			<aside
				class="flex w-2/12 shrink flex-col items-center justify-center gap-1 border-x-2 border-x-gray-300 px-4 text-center"
			>
				<img src={logoCombined} alt="Logo Text" class="w-3/4" />
			</aside>

			{#if invoiceState}
				<aside class="w-5/12 shrink-0 grow p-4 text-xs">
					<div class="mb-2 grid grid-cols-2 items-center gap-2">
						<div class="text-primary flex items-center gap-2">
							<p class="w-1/2 font-semibold text-nowrap">Tanggal Order</p>
							<p class="w-2">:</p>
							<div class="grow"><p>{self?.created_at.split('T')[0]}</p></div>
						</div>

						<div class="text-primary flex items-center gap-2">
							<p class="w-1/2 font-semibold text-nowrap">Jam</p>
							<p class="w-2">:</p>
							<div class="grow"><p>{self?.created_at.split('T')[1]}</p></div>
						</div>
					</div>

					<div class="mb-2 grid grid-cols-2 items-center gap-2">
						<div class="text-primary flex items-center gap-2">
							<p class="w-1/2 font-semibold text-nowrap">Nomor Order</p>
							<p class="w-2">:</p>
							<div class="grow">
								<p>{self?.nomor_order}</p>
							</div>
						</div>

						<div class="text-primary flex items-center gap-2">
							<p class="w-1/2 font-semibold text-nowrap">Nomor Invoice</p>
							<p class="w-2">:</p>
							<div class="grow">
								<p>{data.invoice?.nomor_invoice}</p>
							</div>
						</div>
					</div>

					<div class="text-primary mb-2 flex items-center gap-2">
						<p class="w-3/12 min-w-fit font-semibold">Nomor HP</p>
						<p class="w-2">:</p>
						<div class="grow">
							<FormField
								name="nomor_hp"
								label="Nomor HP"
								type="text"
								bind:value={invoiceState.order.kendaraan.pemilik.no_telp}
								class="input-sm"
								noLabel
							></FormField>
						</div>
					</div>

					<div class="text-primary mb-2 flex items-center gap-2">
						<p class="w-3/12 min-w-fit font-semibold">Jenis Layanan</p>
						<p class="w-2">:</p>
						<div class="grow">
							<FormField
								type="select"
								name="jenis_layanan"
								id="jenis_layanan"
								class="select-sm"
								bind:value={invoiceState.order.jenis_layanan}
								noLabel
							>
								{#snippet select_options()}
									{#each jenisLayanan as jenis}
										<option value={jenis}>{jenis}</option>
									{/each}
								{/snippet}
							</FormField>
						</div>
					</div>

					<div class="text-primary mb-2 flex items-center gap-2">
						<p class="w-3/12 min-w-fit font-semibold">Alamat</p>
						<p class="w-2">:</p>
						<div class="grow">
							<FormField
								name="alamat"
								label="Alamat"
								type="text"
								bind:value={invoiceState.order.alamat.jalan}
								class="input-sm"
								noLabel
							></FormField>
						</div>
					</div>
				</aside>
			{/if}
		</div>
	</section>

	<section
		class="invoice-background flex flex-col gap-4 border-2 border-gray-100"
		style="background-image: url({logoCombined}), url({logoCombined}), url({logoCombined});"
	>
		<section class="grow overflow-auto">
			<ServiceOrderTable />
		</section>

		{#if invoiceState}
			<section class="mb-2 flex justify-end">
				<div class="w-1/3 text-sm">
					<div class="mb-3 border-y-2 border-y-gray-300 py-3">
						<div class="text-primary mb-2 flex items-center gap-2">
							<p class="w-3/12 min-w-fit font-semibold">Subtotal</p>
							<p class="w-2">:</p>
							<p>{rupiah(invoiceState.subtotal)}</p>
						</div>

						<div class="text-primary mb-2 flex items-center gap-2">
							<p class="w-3/12 min-w-fit shrink-0 font-semibold">Diskon</p>
							<p class="w-2">:</p>
							<div class="flex shrink-0 items-center gap-2">
								<FormField
									noLabel
									name="diskon_percentage"
									type="number"
									bind:value={invoiceState.discountByPercentage}
									class="input-xs w-16"
								></FormField>

								<p class="shrink-0">% <span class="text-xs">atau</span></p>

								<Currency
									name="diskon_value"
									id="diskon_value"
									bind:value={invoiceState.discountByValue}
									class="input-xs w-32"
								/>
							</div>
						</div>

						<div class="text-primary flex items-center gap-2">
							<p class="w-3/12 min-w-fit font-semibold">PPN</p>
							<p class="w-2">:</p>
							<div class="flex shrink-0 items-center gap-2">
								<FormField
									noLabel
									name="ppn"
									type="number"
									bind:value={invoiceState.ppn}
									class="input-xs w-16"
								></FormField>
								<p>%</p>
							</div>
						</div>
					</div>

					<div class="text-primary mb-4 flex items-center gap-2 text-base">
						<p class="w-3/12 min-w-fit font-semibold">Total</p>
						<p class="w-2">:</p>
						<p>{rupiah(invoiceState.total)}</p>
					</div>
				</div>
			</section>
		{/if}
	</section>

	<footer class="flex h-1/12 items-center justify-end gap-2 p-4">
		<button class="btn btn-primary">
			<Icon icon="ri:download-2-fill" /> Simpan
		</button>
	</footer>
</main>

<style>
	.invoice-background {
		background-size: 150px;
		background-color: rgba(255, 255, 255, 0.95);
		background-blend-mode: lighten;
		background-position: 0 0;
		background-repeat: repeat;
	}
</style>
