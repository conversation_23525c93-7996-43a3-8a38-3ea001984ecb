<script lang="ts">
	import type { Jasa } from '$lib/schema/general';

	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import { getUtilityModalState } from '$lib/utils/utility/utilityModalState.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { getInvoiceState } from '../../../serviceOrder/InvoiceState.svelte';

	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
</script>

<UtilityModal url="/jasa">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Jasa</h2>
	{/snippet}

	{#snippet item({ item }: { item: Jasa })}
		{@const alreadySelected = invoiceState.service_order.find(
			(order) => order.kind === 'jasa' && (order.data as Jasa).id_jasa === item.id_jasa
		)}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start {alreadySelected
				? 'btn-disabled'
				: ''}"
			onclick={() => {
				invoiceState.service_order.push({
					kind: 'jasa',
					data: item,
					qty: 1,
					harga: item.harga,
					montir: null
				});
				utilityModalState.modal?.close();
			}}
		>
			{item.nama_jasa} <span class="font-light"> @ {rupiah(item.harga)}</span>
		</button>
	{/snippet}
</UtilityModal>
