import { Schema } from 'effect';

export const NoTelpSchema = Schema.NonEmptyString.annotations({
	message: () => 'Nomor Telepon Tidak Boleh Kosong'
})
	.pipe(Schema.minLength(7, { message: () => 'Nomor Telepon Harus Memiliki Minimal 7 Digit' }))
	.pipe(Schema.maxLength(15, { message: () => 'Nomor Telepon Harus Memiliki Maksimal 15 Digit' }))
	.pipe(
		Schema.pattern(/^\+\d{1,3}-\d{7,15}$/, {
			message: () => 'Nomor Telepon Harus Mengandung Kode Negara (e.g. +62) lalu Diikuti "-"'
		})
	);

export const UsernameSchema = Schema.NonEmptyTrimmedString.annotations({
	message: () => 'Username Tidak Boleh Kosong'
})
	.pipe(
		Schema.pattern(/^[a-z0-9_]+$/, {
			message: () => 'Username Hanya Boleh Mengandung Huru<PERSON>, <PERSON><PERSON>, dan Underscore'
		})
	)
	.pipe(Schema.minLength(4, { message: () => 'Username Harus Memiliki Minimal 4 Karakter' }));

export const PasswordSchema = Schema.NonEmptyString.annotations({
	message: () => 'Password Tidak Boleh Kosong'
}).pipe(Schema.minLength(6, { message: () => 'Password Harus Memiliki Minimal 6 Karakter' }));

export const DurasiSchema = Schema.Number.annotations({
	message: () => 'Durasi Estimasi Tidak Boleh Kosong'
}).pipe(Schema.greaterThan(0, { message: () => 'Durasi Estimasi Harus Lebih Dari 0 Menit' }));

// Declare a schema for the File type with additional annotations
export const FileFromSelf = Schema.declare(
	(input: unknown): input is File => input instanceof File,
	{
		// A unique identifier for the schema
		identifier: 'FileFromSelf',
		// Detailed description of the schema
		description: 'The `File` type in JavaScript',
		message: () => 'File Tidak Boleh Kosong'
	}
)
	.pipe(Schema.filter((file) => file.size > 0, { message: () => 'File Tidak Boleh Kosong' }))
	.pipe(
		Schema.filter((file) => file.size < 5 * 1000000, {
			message: () => 'File Tidak Boleh Lebih Dari 5 MB'
		})
	)
	.pipe(
		Schema.filter(
			(file) =>
				[
					'image/png',
					'image/jpeg',
					'image/jpg',
					'image/svg+xml',
					'image/gif',
					'image/webp'
				].includes(file.type),
			{ message: () => 'File Hanya Boleh Berupa Gambar' }
		)
	)
	.pipe(Schema.mutable);

export const MetadataSchema = Schema.Struct({
	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Metadata extends Schema.Schema.Type<typeof MetadataSchema> {}
export interface MetadataEncoded extends Schema.Schema.Encoded<typeof MetadataSchema> {}
