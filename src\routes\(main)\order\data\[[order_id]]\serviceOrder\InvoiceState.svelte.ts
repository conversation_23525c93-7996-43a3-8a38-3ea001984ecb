import type { Jasa, Montir, Paket, Sparepart } from '$lib/schema/general';
import { _Invoice, _Order, type Invoice, type Order } from '$lib/schema/order';
import { getContext, setContext } from 'svelte';

export interface CustomService {
	nama: string;
	harga: number;
}

export type Service = {
	kind: 'paket' | 'jasa' | 'sparepart' | 'custom';
	data: Paket | Jasa | Sparepart | CustomService;
	qty: number;
	harga: number;
	montir: Montir | null;
};

export interface InvoiceState {
	order: Order;
	invoice: Invoice;

	subtotal: number;
	total: number;
	discountByPercentage: number;
	discountByValue: number;
	ppn: number;

	service_order: [[Service], [Service], [Service], [Service]];
}

export default class InvoiceStateClass {
	order = $state<Order>(_Order);
	invoice = $state<Invoice>(_Invoice);

	#discountByPercentage = $state(this.invoice.order.diskon ?? 0);
	#discountByValue = $state(0);
	ppn = $state(this.invoice.order.pajak);

	service_order = $state<Service[]>([]);

	subtotal = $state(1000000);
	total = $derived(
		this.subtotal - this.discountByValue + (this.subtotal - this.discountByValue) * (this.ppn / 100)
	);

	constructor(_invoice: Invoice) {
		if (!_invoice.nomor_invoice) this.order = _Order;
		else this.order = _invoice.order;
	}

	//

	get discountByPercentage() {
		return this.#discountByPercentage;
	}

	set discountByPercentage(v: number) {
		this.#discountByPercentage = v;
		this.#discountByValue = this.subtotal * (v / 100);
	}

	get discountByValue() {
		return this.#discountByValue;
	}

	set discountByValue(v: number) {
		this.#discountByValue = v;
		this.#discountByPercentage = (v / this.subtotal) * 100;
	}
}

const INVOICE_STATE_KEY = Symbol('@@invoice-state@@');

export function setInvoiceState(_invoice: Invoice): void {
	setContext(INVOICE_STATE_KEY, new InvoiceStateClass(_invoice));
}

export function getInvoiceState(): InvoiceState {
	return getContext<InvoiceState>(INVOICE_STATE_KEY);
}
